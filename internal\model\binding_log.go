package model

import (
	"time"
	"gorm.io/gorm"
)

// BindingLog 绑卡日志模型
type BindingLog struct {
	// 基础字段
	ID        uint           `gorm:"primarykey" json:"id"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`

	// 日志信息
	CardNumber    string    `gorm:"column:card_number;type:varchar(50);not null;comment:卡号" json:"card_number"`
	MerchantID    uint      `gorm:"column:merchant_id;not null;comment:商户ID" json:"merchant_id"`
	UserID        uint      `gorm:"column:user_id;not null;comment:用户ID" json:"user_id"`
	Action        string    `gorm:"column:action;type:varchar(50);not null;comment:操作类型" json:"action"`
	Status        string    `gorm:"column:status;type:varchar(20);not null;comment:状态" json:"status"`
	Message       string    `gorm:"column:message;type:text;comment:消息" json:"message"`
	RequestData   string    `gorm:"column:request_data;type:json;comment:请求数据" json:"request_data"`
	ResponseData  string    `gorm:"column:response_data;type:json;comment:响应数据" json:"response_data"`
	ErrorCode     string    `gorm:"column:error_code;type:varchar(50);comment:错误代码" json:"error_code"`
	ErrorMessage  string    `gorm:"column:error_message;type:text;comment:错误消息" json:"error_message"`
	ProcessTime   int       `gorm:"column:process_time;comment:处理时间(毫秒)" json:"process_time"`
	IPAddress     string    `gorm:"column:ip_address;type:varchar(45);comment:IP地址" json:"ip_address"`
	UserAgent     string    `gorm:"column:user_agent;type:text;comment:用户代理" json:"user_agent"`
	Level         string    `gorm:"column:level;type:varchar(20);default:info;comment:日志级别" json:"level"`
	LogType       string    `gorm:"column:log_type;type:varchar(50);default:binding;comment:日志类型" json:"log_type"`
}

// TableName 指定表名
func (BindingLog) TableName() string {
	return "binding_logs"
}