from typing import List, Dict, Any, Optional, Union
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.logging import get_logger
from app.models.card_record import CardRecord
from app.models.binding_log import BindingLog, LogLevel, LogType
from app.crud import card as card_crud
from app.crud.binding_log import CRUDBindingLog
from app.schemas.binding_timeline import (
    BindingTimelineResponse,
    BindingStepDetail,
    BindingStepSummary,
    BindingPerformanceAnalysis,
    StepTypeStatistics
)
from app.utils.time_utils import format_duration, ensure_timezone, get_current_time

logger = get_logger("binding_timeline_service")


class BindingTimelineService:
    """绑卡时间线分析服务"""

    def __init__(self, db: Union[Session, AsyncSession]):
        self.db = db
        self.binding_log_crud = CRUDBindingLog(BindingLog)

    def get_binding_timeline(self, card_record_id: str) -> BindingTimelineResponse:
        """
        获取绑卡记录的完整时间线
        
        Args:
            card_record_id: 绑卡记录ID
            
        Returns:
            BindingTimelineResponse: 绑卡时间线详情
        """
        # 获取绑卡记录
        card_record = card_crud.get_card_record(self.db, card_record_id)
        if not card_record:
            raise ValueError(f"绑卡记录不存在: {card_record_id}")

        # 获取所有相关日志
        logs = self.binding_log_crud.get_by_card_record_id(
            self.db, card_record_id, skip=0, limit=1000
        )

        # 按时间排序（最早的在前面）
        logs.sort(key=lambda x: x.timestamp)

        # 构建步骤详情
        steps = self._build_step_details(logs)

        # 计算总耗时
        start_time = ensure_timezone(card_record.created_at)
        end_time = ensure_timezone(card_record.updated_at) if card_record.updated_at else get_current_time()
        total_duration_ms = (end_time - start_time).total_seconds() * 1000

        # 生成统计摘要
        summary = self._generate_summary(steps, logs)

        return BindingTimelineResponse(
            card_record_id=card_record_id,
            card_number=self._mask_card_number(card_record.card_number),
            total_duration=total_duration_ms / 1000,  # 秒
            total_duration_ms=total_duration_ms,      # 毫秒
            total_duration_formatted=format_duration(total_duration_ms / 1000),
            overall_status=card_record.status,
            start_time=start_time,
            end_time=end_time if card_record.status in ['success', 'failed'] else None,
            steps=steps,
            summary=summary
        )

    def _build_step_details(self, logs: List[BindingLog]) -> List[BindingStepDetail]:
        """构建步骤详情列表"""
        steps = []

        for i, log in enumerate(logs):
            # 确定步骤状态
            status = self._determine_step_status(log)

            # 计算步骤耗时
            duration_ms = self._calculate_step_duration(log, logs, i)

            # 格式化耗时
            duration_formatted = None
            if duration_ms is not None:
                duration_formatted = format_duration(duration_ms / 1000)

            # 计算结束时间
            end_time = None
            if duration_ms is not None:
                start_time = ensure_timezone(log.timestamp)
                end_time = start_time + timedelta(milliseconds=duration_ms)

            # 提取错误信息
            error_message = None
            if log.log_level == LogLevel.ERROR and log.details:
                error_message = log.details.get('error_message') or log.details.get('error')

            step = BindingStepDetail(
                step_name=self._get_step_name(log),
                step_type=log.log_type,
                log_level=log.log_level,
                message=log.message,
                start_time=ensure_timezone(log.timestamp),
                end_time=end_time,
                duration_ms=duration_ms,
                duration_formatted=duration_formatted,
                status=status,
                details=log.details,
                request_data=log.request_data,
                response_data=log.response_data,
                attempt_number=log.attempt_number,
                walmart_ck_id=log.walmart_ck_id,
                error_message=error_message
            )
            steps.append(step)

        return steps

    def _calculate_step_duration(self, current_log: BindingLog, all_logs: List[BindingLog], current_index: int) -> Optional[float]:
        """
        计算步骤耗时（毫秒）

        优先级：
        1. 如果log.duration_ms存在，直接使用
        2. 如果不存在，计算与下一个日志的时间差
        3. 如果是最后一个日志，返回None（由总耗时覆盖）
        """
        # 优先使用已记录的duration_ms
        if current_log.duration_ms is not None:
            return current_log.duration_ms

        # 如果没有记录duration_ms，尝试计算时间差
        if current_index < len(all_logs) - 1:
            # 不是最后一个日志，计算与下一个日志的时间差
            next_log = all_logs[current_index + 1]
            current_time = ensure_timezone(current_log.timestamp)
            next_time = ensure_timezone(next_log.timestamp)

            # 计算时间差（毫秒）
            time_diff = (next_time - current_time).total_seconds() * 1000

            # 确保时间差为正数且合理（不超过1小时）
            if 0 <= time_diff <= 3600000:  # 1小时 = 3600000毫秒
                return time_diff

        # 无法计算耗时，返回None
        return None

    def _determine_step_status(self, log: BindingLog) -> str:
        """确定步骤状态"""
        if log.log_level == LogLevel.ERROR:
            return "failed"
        elif log.log_level in [LogLevel.INFO, LogLevel.DEBUG] and log.duration_ms is not None:
            return "success"
        else:
            return "running"

    def _get_step_name(self, log: BindingLog) -> str:
        """获取步骤名称 - 基于日志消息内容生成具体的步骤名称"""

        # 优先根据消息内容生成具体的步骤名称
        message = log.message or ""

        # 系统操作 - 根据消息内容细化
        if log.log_type == LogType.SYSTEM:
            if "队列接收" in message or "接收到绑卡任务" in message:
                return "接收绑卡任务"
            elif "开始处理绑卡请求" in message:
                return "开始处理绑卡"
            elif "开始恢复处理" in message:
                return "恢复处理绑卡"
            elif "绑卡请求已创建" in message:
                return "创建绑卡记录"
            elif "等待队列处理" in message:
                return "加入处理队列"
            elif "处理完成" in message:
                return "处理完成"
            else:
                return f"系统操作: {message}"

        # API请求 - 显示具体的API类型
        elif log.log_type == LogType.API_REQUEST:
            if "沃尔玛API请求" in message:
                attempt = log.attempt_number or "1"
                return f"沃尔玛API请求 (第{attempt}次)"
            else:
                return f"API请求: {message}"

        # API响应 - 显示响应结果
        elif log.log_type == LogType.API_RESPONSE:
            if "成功" in message:
                return "API响应成功"
            elif "错误" in message or "失败" in message:
                return "API响应失败"
            else:
                return f"API响应: {message}"

        # 沃尔玛请求
        elif log.log_type == LogType.WALMART_REQUEST:
            if "开始绑卡尝试" in message:
                attempt = log.attempt_number or "1"
                ck_id = ""
                if "CK_ID=" in message:
                    ck_id = message.split("CK_ID=")[1].split(" ")[0]
                    ck_id = f" (CK:{ck_id})"
                return f"绑卡尝试{ck_id} (第{attempt}次)"
            elif "沃尔玛API请求" in message:
                attempt = log.attempt_number or "1"
                return f"发送绑卡请求 (第{attempt}次)"
            else:
                return f"沃尔玛请求: {message}"

        # 沃尔玛响应
        elif log.log_type == LogType.WALMART_RESPONSE:
            if "成功" in message:
                return "绑卡成功"
            elif "错误" in message or "失败" in message:
                if "请先去登录" in message:
                    return "登录失效"
                elif "卡号或密码错误" in message:
                    return "卡号密码错误"
                elif "系统繁忙" in message:
                    return "系统繁忙"
                else:
                    return "绑卡失败"
            else:
                return f"沃尔玛响应: {message}"

        # 绑卡尝试结果
        elif log.log_type == LogType.BIND_ATTEMPT:
            attempt = log.attempt_number or "1"
            if "成功" in message:
                return f"绑卡成功 (第{attempt}次)"
            elif "失败" in message:
                if "系统繁忙" in message:
                    return f"系统繁忙 (第{attempt}次)"
                elif "CK已失效" in message or "CK已被禁用" in message:
                    return f"CK失效 (第{attempt}次)"
                else:
                    return f"绑卡失败 (第{attempt}次)"
            else:
                return f"绑卡尝试 (第{attempt}次)"

        # 状态变更
        elif log.log_type == LogType.STATUS_CHANGE:
            if "->" in message:
                status_change = message.split("状态变更: ")[1] if "状态变更: " in message else message
                return f"状态变更: {status_change}"
            else:
                return f"状态变更: {message}"

        # 错误处理
        elif log.log_type == LogType.ERROR:
            if "CK" in message:
                return "CK相关错误"
            elif "网络" in message or "连接" in message:
                return "网络连接错误"
            elif "超时" in message:
                return "请求超时"
            else:
                return f"错误处理: {message}"

        # 重试操作
        elif log.log_type == LogType.RETRY:
            attempt = log.attempt_number or "?"
            return f"重试操作 (第{attempt}次)"

        # 回调处理
        elif log.log_type == LogType.CALLBACK:
            return f"回调处理: {message}"

        # 默认情况 - 使用日志类型和完整消息
        else:
            type_name = log.log_type.replace("_", " ").title()
            return f"{type_name}: {message}"

    def _generate_summary(self, steps: List[BindingStepDetail], logs: List[BindingLog]) -> Dict[str, Any]:
        """生成统计摘要"""
        total_steps = len(steps)
        completed_steps = len([s for s in steps if s.status == "success"])
        failed_steps = len([s for s in steps if s.status == "failed"])
        running_steps = len([s for s in steps if s.status == "running"])
        
        # 计算尝试次数
        attempts = set()
        for log in logs:
            if log.attempt_number:
                attempts.add(log.attempt_number)
        total_attempts = len(attempts) if attempts else 1

        # 计算成功率
        success_rate = (completed_steps / total_steps * 100) if total_steps > 0 else 0

        # 计算平均耗时
        durations = [s.duration_ms for s in steps if s.duration_ms is not None]
        average_duration = sum(durations) / len(durations) if durations else None

        # 找出最长步骤
        longest_step = None
        longest_duration = 0
        for step in steps:
            if step.duration_ms and step.duration_ms > longest_duration:
                longest_duration = step.duration_ms
                longest_step = step.step_name

        return {
            "total_steps": total_steps,
            "completed_steps": completed_steps,
            "failed_steps": failed_steps,
            "running_steps": running_steps,
            "total_attempts": total_attempts,
            "success_rate": round(success_rate, 2),
            "average_step_duration_ms": round(average_duration, 2) if average_duration else None,
            "longest_step_duration_ms": longest_duration if longest_duration > 0 else None,
            "longest_step_name": longest_step
        }

    def _mask_card_number(self, card_number: str) -> str:
        """脱敏卡号"""
        if not card_number or len(card_number) < 6:
            return card_number
        return card_number[:6] + "*" * (len(card_number) - 6)

    def get_performance_analysis(self, card_record_id: str) -> BindingPerformanceAnalysis:
        """
        获取绑卡性能分析
        
        Args:
            card_record_id: 绑卡记录ID
            
        Returns:
            BindingPerformanceAnalysis: 性能分析结果
        """
        timeline = self.get_binding_timeline(card_record_id)
        
        # 分析性能指标
        performance_metrics = self._analyze_performance_metrics(timeline.steps)
        
        # 识别性能瓶颈
        bottlenecks = self._identify_bottlenecks(timeline.steps)
        
        # 生成优化建议
        recommendations = self._generate_recommendations(timeline.steps, bottlenecks)

        return BindingPerformanceAnalysis(
            card_record_id=card_record_id,
            timeline=timeline,
            performance_metrics=performance_metrics,
            bottlenecks=bottlenecks,
            recommendations=recommendations
        )

    def _analyze_performance_metrics(self, steps: List[BindingStepDetail]) -> Dict[str, Any]:
        """分析性能指标"""
        durations = [s.duration_ms for s in steps if s.duration_ms is not None]
        
        if not durations:
            return {"message": "无可用的性能数据"}

        return {
            "total_steps": len(steps),
            "steps_with_timing": len(durations),
            "min_duration_ms": min(durations),
            "max_duration_ms": max(durations),
            "avg_duration_ms": sum(durations) / len(durations),
            "total_measured_duration_ms": sum(durations),
            "step_types": self._get_step_type_statistics(steps)
        }

    def _get_step_type_statistics(self, steps: List[BindingStepDetail]) -> List[Dict[str, Any]]:
        """获取步骤类型统计"""
        type_stats = {}
        
        for step in steps:
            step_type = step.step_type
            if step_type not in type_stats:
                type_stats[step_type] = {
                    "count": 0,
                    "total_duration_ms": 0,
                    "success_count": 0,
                    "failure_count": 0
                }
            
            type_stats[step_type]["count"] += 1
            if step.duration_ms:
                type_stats[step_type]["total_duration_ms"] += step.duration_ms
            
            if step.status == "success":
                type_stats[step_type]["success_count"] += 1
            elif step.status == "failed":
                type_stats[step_type]["failure_count"] += 1

        # 转换为列表格式
        result = []
        for step_type, stats in type_stats.items():
            avg_duration = stats["total_duration_ms"] / stats["count"] if stats["count"] > 0 else 0
            success_rate = stats["success_count"] / stats["count"] * 100 if stats["count"] > 0 else 0
            
            result.append({
                "step_type": step_type,
                "count": stats["count"],
                "total_duration_ms": stats["total_duration_ms"],
                "average_duration_ms": round(avg_duration, 2),
                "success_count": stats["success_count"],
                "failure_count": stats["failure_count"],
                "success_rate": round(success_rate, 2)
            })

        return sorted(result, key=lambda x: x["total_duration_ms"], reverse=True)

    def _identify_bottlenecks(self, steps: List[BindingStepDetail]) -> List[Dict[str, Any]]:
        """识别性能瓶颈"""
        bottlenecks = []
        
        # 找出耗时最长的步骤
        steps_with_duration = [s for s in steps if s.duration_ms is not None]
        if steps_with_duration:
            avg_duration = sum(s.duration_ms for s in steps_with_duration) / len(steps_with_duration)
            
            for step in steps_with_duration:
                if step.duration_ms > avg_duration * 2:  # 超过平均值2倍的认为是瓶颈
                    bottlenecks.append({
                        "step_name": step.step_name,
                        "duration_ms": step.duration_ms,
                        "duration_formatted": step.duration_formatted,
                        "severity": "high" if step.duration_ms > avg_duration * 5 else "medium",
                        "description": f"该步骤耗时 {step.duration_formatted}，超过平均值 {format_duration(avg_duration/1000)}"
                    })

        return sorted(bottlenecks, key=lambda x: x["duration_ms"], reverse=True)

    def _generate_recommendations(self, steps: List[BindingStepDetail], bottlenecks: List[Dict[str, Any]]) -> List[str]:
        """生成优化建议"""
        recommendations = []
        
        # 基于瓶颈生成建议
        for bottleneck in bottlenecks:
            if "API" in bottleneck["step_name"]:
                recommendations.append(f"优化 {bottleneck['step_name']} 的网络请求，考虑增加超时设置或重试机制")
            elif "绑卡尝试" in bottleneck["step_name"]:
                recommendations.append(f"分析 {bottleneck['step_name']} 的处理逻辑，可能需要优化绑卡算法")

        # 基于失败步骤生成建议
        failed_steps = [s for s in steps if s.status == "failed"]
        if failed_steps:
            recommendations.append(f"发现 {len(failed_steps)} 个失败步骤，建议检查错误处理机制")

        # 基于重试次数生成建议
        retry_steps = [s for s in steps if s.attempt_number and int(s.attempt_number) > 1]
        if retry_steps:
            recommendations.append(f"发现 {len(retry_steps)} 次重试操作，建议优化初次成功率")

        return recommendations if recommendations else ["当前绑卡流程运行良好，无明显优化点"]
