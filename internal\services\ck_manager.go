package services

import (
	"fmt"
	"time"

	"walmart-bind-card-processor/internal/model"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// CKManagerService CK管理服务
type CKManagerService struct {
	db     *gorm.DB
	logger *logrus.Logger
}

// NewCKManagerService 创建CK管理服务
func NewCKManagerService(db *gorm.DB, logger *logrus.Logger) *CKManagerService {
	return &CKManagerService{
		db:     db,
		logger: logger,
	}
}

// DisableCK 禁用CK
func (s *CKManagerService) DisableCK(ckID uint, reason string) error {
	s.logger.WithFields(logrus.Fields{
		"ck_id":  ckID,
		"reason": reason,
	}).Warning("禁用CK")

	result := s.db.Model(&model.WalmartCK{}).
		Where("id = ?", ckID).
		Update("active", false)

	if result.Error != nil {
		s.logger.WithError(result.Error).Error("禁用CK失败")
		return fmt.Errorf("禁用CK失败: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		s.logger.Warning("CK不存在或已被禁用")
		return fmt.Errorf("CK %d 不存在或已被禁用", ckID)
	}

	s.logger.WithField("ck_id", ckID).Info("CK已成功禁用")
	return nil
}

// GetNextAvailableCK 获取下一个可用CK
func (s *CKManagerService) GetNextAvailableCK(merchantID uint, excludeIDs []uint) (*model.WalmartCK, error) {
	s.logger.WithFields(logrus.Fields{
		"merchant_id": merchantID,
		"exclude_ids": excludeIDs,
	}).Info("获取下一个可用CK")

	query := s.db.Where(
		"merchant_id = ? AND active = ? AND is_deleted = ? AND bind_count < total_limit",
		merchantID, true, false,
	)

	if len(excludeIDs) > 0 {
		query = query.Where("id NOT IN ?", excludeIDs)
	}

	var ck model.WalmartCK
	err := query.Order("bind_count ASC, last_bind_time ASC").First(&ck).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			s.logger.Warning("没有找到可用的CK")
			return nil, fmt.Errorf("商户 %d 没有可用的CK", merchantID)
		}
		s.logger.WithError(err).Error("查询CK失败")
		return nil, fmt.Errorf("查询CK失败: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"ck_id":      ck.ID,
		"bind_count": ck.BindCount,
		"total_limit": ck.TotalLimit,
	}).Info("找到可用CK")

	return &ck, nil
}

// GetAvailableCKWithWeight 基于权重算法获取可用CK
// 注意：此方法为兼容性保留，主要绑卡流程请使用 CKWeightManager
func (s *CKManagerService) GetAvailableCKWithWeight(merchantID uint, departmentID *uint) (*model.WalmartCK, error) {
	s.logger.WithFields(logrus.Fields{
		"merchant_id":   merchantID,
		"department_id": departmentID,
	}).Info("基于权重算法获取可用CK（兼容模式）")

	// 如果指定了部门ID，直接从该部门获取
	if departmentID != nil {
		return s.getCKFromDepartment(merchantID, *departmentID)
	}

	// 简化实现：查找第一个有可用CK的启用部门
	var departments []model.Department
	err := s.db.Where(
		"merchant_id = ? AND status = ? AND enable_binding = ? AND binding_weight > 0",
		merchantID, true, true,
	).Order("binding_weight DESC").Find(&departments).Error

	if err != nil {
		s.logger.WithError(err).Error("查询部门失败")
		return nil, fmt.Errorf("查询部门失败: %w", err)
	}

	// 遍历部门，找到第一个有可用CK的部门
	for _, dept := range departments {
		ck, err := s.getCKFromDepartment(merchantID, dept.ID)
		if err == nil && ck != nil {
			s.logger.WithFields(logrus.Fields{
				"selected_department_id":   dept.ID,
				"selected_department_name": dept.Name,
				"binding_weight":           dept.BindingWeight,
			}).Info("简化权重算法选择部门成功")
			return ck, nil
		}
	}

	s.logger.Warning("没有可用的部门或CK")
	return nil, fmt.Errorf("商户 %d 没有可用的部门或CK", merchantID)
}

// getCKFromDepartment 从指定部门获取CK
func (s *CKManagerService) getCKFromDepartment(merchantID, departmentID uint) (*model.WalmartCK, error) {
	s.logger.WithFields(logrus.Fields{
		"merchant_id":   merchantID,
		"department_id": departmentID,
	}).Info("从指定部门获取CK")

	var ck model.WalmartCK
	err := s.db.Where(
		"merchant_id = ? AND department_id = ? AND active = ? AND is_deleted = ? AND bind_count < total_limit",
		merchantID, departmentID, true, false,
	).Order("bind_count ASC, last_bind_time ASC").First(&ck).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			s.logger.WithFields(logrus.Fields{
				"merchant_id":   merchantID,
				"department_id": departmentID,
			}).Warning("部门没有可用的CK")
			return nil, fmt.Errorf("部门 %d 没有可用的CK", departmentID)
		}
		s.logger.WithError(err).Error("查询部门CK失败")
		return nil, fmt.Errorf("查询部门CK失败: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"ck_id":         ck.ID,
		"department_id": departmentID,
		"bind_count":    ck.BindCount,
		"total_limit":   ck.TotalLimit,
	}).Info("从部门获取CK成功")

	return &ck, nil
}

// PreOccupyCK 预占用CK（增加绑卡计数）
func (s *CKManagerService) PreOccupyCK(ckID uint) error {
	s.logger.WithField("ck_id", ckID).Info("预占用CK")

	result := s.db.Model(&model.WalmartCK{}).
		Where("id = ? AND active = ? AND is_deleted = ?", ckID, true, false).
		Update("bind_count", gorm.Expr("bind_count + 1"))

	if result.Error != nil {
		s.logger.WithError(result.Error).Error("预占用CK失败")
		return fmt.Errorf("预占用CK失败: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		s.logger.Warning("CK不存在或不可用")
		return fmt.Errorf("CK %d 不存在或不可用", ckID)
	}

	s.logger.WithField("ck_id", ckID).Info("CK预占用成功")
	return nil
}

// CommitCKUsage 确认CK使用（绑卡成功）或回滚（绑卡失败）
func (s *CKManagerService) CommitCKUsage(ckID uint, success bool) error {
	s.logger.WithFields(logrus.Fields{
		"ck_id":   ckID,
		"success": success,
	}).Info("确认CK使用结果")

	if success {
		// 绑卡成功：更新最后绑卡时间，检查是否需要禁用
		now := time.Now()
		updates := map[string]interface{}{
			"last_bind_time": &now,
		}

		// 检查是否达到限制
		var ck model.WalmartCK
		if err := s.db.First(&ck, ckID).Error; err != nil {
			s.logger.WithError(err).Error("查询CK失败")
			return fmt.Errorf("查询CK失败: %w", err)
		}

		if ck.BindCount >= ck.TotalLimit {
			updates["active"] = false
			s.logger.WithFields(logrus.Fields{
				"ck_id":       ckID,
				"bind_count":  ck.BindCount,
				"total_limit": ck.TotalLimit,
			}).Info("CK达到限制，自动禁用")
		}

		result := s.db.Model(&model.WalmartCK{}).Where("id = ?", ckID).Updates(updates)
		if result.Error != nil {
			s.logger.WithError(result.Error).Error("更新CK状态失败")
			return fmt.Errorf("更新CK状态失败: %w", result.Error)
		}

		s.logger.WithField("ck_id", ckID).Info("绑卡成功，CK状态已更新")
	} else {
		// 绑卡失败：回滚预占用的计数
		result := s.db.Model(&model.WalmartCK{}).
			Where("id = ? AND bind_count > 0", ckID).
			Update("bind_count", gorm.Expr("bind_count - 1"))

		if result.Error != nil {
			s.logger.WithError(result.Error).Error("回滚CK计数失败")
			return fmt.Errorf("回滚CK计数失败: %w", result.Error)
		}

		if result.RowsAffected == 0 {
			s.logger.WithField("ck_id", ckID).Warning("CK计数已为0，无法回滚")
		} else {
			s.logger.WithField("ck_id", ckID).Info("绑卡失败，CK计数已回滚")
		}
	}

	return nil
}

// GetCKStats 获取CK统计信息
func (s *CKManagerService) GetCKStats(merchantID uint) (map[string]interface{}, error) {
	s.logger.WithField("merchant_id", merchantID).Info("获取CK统计信息")

	var stats struct {
		TotalCKs     int64 `json:"total_cks"`
		ActiveCKs    int64 `json:"active_cks"`
		AvailableCKs int64 `json:"available_cks"`
		DisabledCKs  int64 `json:"disabled_cks"`
	}

	// 总CK数
	s.db.Model(&model.WalmartCK{}).
		Where("merchant_id = ? AND is_deleted = ?", merchantID, false).
		Count(&stats.TotalCKs)

	// 激活的CK数
	s.db.Model(&model.WalmartCK{}).
		Where("merchant_id = ? AND is_deleted = ? AND active = ?", merchantID, false, true).
		Count(&stats.ActiveCKs)

	// 可用的CK数（激活且未达到限制）
	s.db.Model(&model.WalmartCK{}).
		Where("merchant_id = ? AND is_deleted = ? AND active = ? AND bind_count < total_limit", 
			merchantID, false, true).
		Count(&stats.AvailableCKs)

	// 禁用的CK数
	s.db.Model(&model.WalmartCK{}).
		Where("merchant_id = ? AND is_deleted = ? AND active = ?", merchantID, false, false).
		Count(&stats.DisabledCKs)

	result := map[string]interface{}{
		"total_cks":     stats.TotalCKs,
		"active_cks":    stats.ActiveCKs,
		"available_cks": stats.AvailableCKs,
		"disabled_cks":  stats.DisabledCKs,
	}

	s.logger.WithFields(logrus.Fields{
		"merchant_id": merchantID,
		"stats":       result,
	}).Info("CK统计信息获取成功")

	return result, nil
}
